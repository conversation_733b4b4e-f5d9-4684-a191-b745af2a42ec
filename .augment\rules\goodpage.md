---
type: "always_apply"
priority: 1000
---

# 🚀 GoodPage 项目开发核心参考

> **现代化实验室主页项目 - 企业级前端工程化最佳实践**

## 📋 项目技术栈

**核心框架:** Next.js 15 (App Router) + TypeScript + Tailwind CSS
**状态管理:** Zustand + React Context + useState/useReducer
**UI 组件:** shadcn/ui + Framer Motion + lucide-react
**数据层:** Prisma ORM + SQLite + Zod 验证
**开发工具:** ESLint + Prettier + Husky + lint-staged

---

## 🎯 强制执行规范

### 📚 必读文档 (执行任何操作前必须查阅)

1. **开发规范文档:** `@GoodPage/Development_Specification.md`
   - 代码风格与质量保障 (Prettier/ESLint 配置)
   - 命名规范 (文件、变量、函数、组件、类型)
   - 注释规范 (中文注释、JSDoc)
   - 颜色管理 (统一通过 `src/styles/theme.ts`)
   - 组件设计原则 (拆分策略、状态管理、Props 类型)
   - API 交互模式 (数据获取、错误处理、响应格式)
   - 测试策略 (单元测试、集成测试、E2E 测试)

2. **设计文档:** `@GoodPage/design.md`
   - 项目架构与功能模块规划
   - 开发者页面访问机制 (Konami Code 触发)
   - 认证设计与权限控制 (RBAC)
   - 技术选型决策与实现方案

### 🗂️ 项目结构约定

- **项目根目录:** 所有代码和文件操作基于 `GoodPage/` 子目录
- **路径别名:** 使用 `@/*` 指向 `src/*`
- **组件组织:** 按页面/功能模块分组，遵循 Next.js App Router 约定
- **文件命名:** 严格遵循规范文档中的命名约定

---

## 🛠️ 代码开发规范

### 代码质量保障
- **格式化工具:** 强制使用 Prettier，配置文件必须纳入版本控制
- **代码检查:** 基于 `next/core-web-vitals` + TypeScript ESLint 规则
- **提交钩子:** 配置 Husky + lint-staged，pre-commit 自动检查
- **类型安全:** 充分利用 TypeScript，避免 `any` 类型

### 组件开发原则
- **Props 定义:** 强制使用 TypeScript `type` 关键字定义组件 Props
- **状态管理优先级:** `useState` → `useReducer` → Context API → Zustand
- **组件拆分:** 遵循单一职责原则，关注点分离，避免过长文件 (>300-500行)
- **模块化设计:** 功能内聚，耦合度低，便于测试和维护

### API 开发规范
- **路由结构:** 遵循 Next.js App Router API 约定
- **数据库操作:** 使用 Prisma ORM，确保类型安全
- **错误处理:** 统一错误响应格式，适当的 HTTP 状态码
- **输入验证:** 前后端双重验证，使用 Zod 进行数据验证

---

## 🎨 UI/UX 开发要求

### 样式管理
- **CSS 框架:** Tailwind CSS 为主，shadcn/ui 组件库补充
- **颜色管理:** 严禁硬编码颜色，统一通过 `src/styles/theme.ts` 管理
- **响应式设计:** Mobile-first 策略，合理使用 Tailwind 断点
- **动画效果:** 使用 Framer Motion，注重性能和用户体验

### 组件库使用
- **基础组件:** 优先使用 shadcn/ui 组件，保持设计一致性
- **图标系统:** 统一使用 lucide-react 图标库
- **表单处理:** react-hook-form + Zod 验证，确保类型安全
- **用户反馈:** 使用 Sonner toast 组件提供操作反馈

---

## 🔒 安全与性能要求

### 安全规范
- **环境变量:** 敏感信息通过环境变量配置，提供 `.env.example` 示例
- **权限控制:** 开发者页面严格访问控制，实现 RBAC 权限系统
- **输入验证:** 防止 XSS、SQL 注入等安全漏洞
- **依赖安全:** 定期运行 `npm audit` 检查并修复安全漏洞

### 性能优化
- **代码分割:** 利用 Next.js 自动代码分割和动态导入
- **图片优化:** 使用 Next.js Image 组件，适当的格式和尺寸
- **缓存策略:** 合理使用 Next.js 缓存机制
- **Bundle 优化:** 定期分析打包体积，避免不必要的依赖

---

## 🧪 测试与质量保证

### 测试策略
- **单元测试:** Vitest + React Testing Library (工具函数、API 路由)
- **集成测试:** 关键组件交互流程 (开发者认证、数据管理)
- **E2E 测试:** Playwright (核心用户流程，如开发者登录)
- **测试覆盖率:** 关注关键逻辑，不追求强制高覆盖率

### 代码审查
- **提交规范:** 遵循 Conventional Commits 规范
- **分支策略:** 功能分支开发，主分支保护
- **代码审查:** 重点关注架构设计、性能影响、安全性

---

## 💡 协作与开发流程

### 技术决策流程
- **技术选型:** 新功能开发前必须进行技术调研和团队讨论
- **设计文档:** 重要变更和架构决策及时更新到 `design.md`
- **沟通协作:** 积极讨论技术方案，遇到问题及时寻求团队支持

### AI 助手协作规范
- **命令执行限制:** AI 助手不得主动执行终端命令 (如 `npm install`, `prisma migrate`)
- **操作确认:** 需要执行的命令必须由开发者确认后手动执行
- **后续步骤:** 完成任务后清晰说明下一步关键操作和整体计划

---

## ⚠️ 关键注意事项

1. **文档同步:** 所有设计决策和重要变更必须及时更新相关文档
2. **依赖管理:** 统一使用 npm 作为包管理工具，确保团队环境一致性
3. **版本控制:** 配置文件、环境示例文件必须纳入版本控制
4. **性能意识:** 时刻关注应用性能，避免不必要的重渲染和资源浪费
5. **用户体验:** 优先考虑用户体验，确保界面响应速度和交互流畅性

---

**🎯 项目目标:** 构建高质量、可维护、性能优异的现代化实验室主页系统，展现实验室的技术实力和专业水准。
