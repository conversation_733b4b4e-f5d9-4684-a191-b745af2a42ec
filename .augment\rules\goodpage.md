---
type: "always_apply"
---



# GoodPage 项目开发核心参考

在为本项目提供任何代码建议、修改或重构时，请**严格遵守**以下核心规范文档：

*   **开发规范:** @GoodPage/Development_Specification.md
    *   **关键内容:** 代码风格 (Prettier/ESLint), 命名规范, 注释规范, 颜色管理, 组件设计原则 (拆分, 状态管理, Props类型), API 交互 (数据获取策略, 错误处理, 响应格式), 测试策略, Git工作流, 安全性, 部署要求。
    *   **特别注意:** 所有代码和文件操作均基于 `GoodPage/` 子目录作为项目根目录。
*   **设计文档:** @GoodPage/design.md
    *   **关键内容:** 项目目标, 开发者页面访问机制 (Konami Code), 认证设计, 功能模块规划等。

**请在执行任何操作前，优先查阅并理解相关规范。**
